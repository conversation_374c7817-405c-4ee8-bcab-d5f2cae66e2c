/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #2c3e50;
    background-color: #f8fafc;
    font-size: 14px;
}

/* Container */
.cv-container {
    max-width: 210mm; /* A4 width */
    margin: 0 auto;
    background: white;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    padding: 40px;
    min-height: 297mm; /* A4 height */
}

/* Header */
.header {
    border-bottom: 3px solid #3498db;
    padding-bottom: 30px;
    margin-bottom: 40px;
}

.header-content {
    text-align: center;
}

.name {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 15px;
    letter-spacing: -0.5px;
}

.contact-info {
    display: flex;
    justify-content: center;
    gap: 30px;
    flex-wrap: wrap;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 1rem;
    color: #5a6c7d;
}

.icon {
    font-size: 1.1rem;
}

/* Sections */
.section {
    margin-bottom: 35px;
    page-break-inside: avoid;
}

.section-title {
    font-size: 1.4rem;
    font-weight: 600;
    color: #2c3e50;
    border-bottom: 2px solid #ecf0f1;
    padding-bottom: 8px;
    margin-bottom: 20px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.section-content {
    padding-left: 0;
}

/* Professional Summary */
.section-content p {
    text-align: justify;
    line-height: 1.7;
    color: #34495e;
    font-size: 1rem;
}

/* Experience */
.experience-item {
    margin-bottom: 30px;
    page-break-inside: avoid;
}

.experience-header {
    margin-bottom: 15px;
}

.job-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 5px;
}

.job-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;
}

.company {
    font-weight: 500;
    color: #3498db;
    font-size: 1rem;
}

.duration {
    color: #7f8c8d;
    font-style: italic;
    font-size: 0.95rem;
}

.achievements {
    list-style: none;
    padding-left: 0;
}

.achievements li {
    position: relative;
    padding-left: 20px;
    margin-bottom: 8px;
    line-height: 1.6;
    color: #34495e;
}

.achievements li::before {
    content: "•";
    color: #3498db;
    font-weight: bold;
    position: absolute;
    left: 0;
    top: 0;
}

/* Skills */
.skills-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 12px;
}

.skill-item {
    background: #ecf0f1;
    padding: 12px 16px;
    border-radius: 6px;
    font-weight: 500;
    color: #2c3e50;
    text-align: center;
    border-left: 4px solid #3498db;
}

/* Education and Languages */
.education-list,
.languages-list {
    list-style: none;
    padding-left: 0;
}

.education-list li,
.languages-list li {
    padding: 8px 0;
    border-bottom: 1px solid #ecf0f1;
    color: #34495e;
}

.education-list li:last-child,
.languages-list li:last-child {
    border-bottom: none;
}

/* Download Section */
.download-section {
    text-align: center;
    margin: 40px 0;
    padding: 20px;
}

.download-btn {
    background: #3498db;
    color: white;
    border: none;
    padding: 15px 30px;
    font-size: 1rem;
    font-weight: 600;
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    box-shadow: 0 4px 6px rgba(52, 152, 219, 0.3);
}

.download-btn:hover {
    background: #2980b9;
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(52, 152, 219, 0.4);
}

/* Print Styles */
@media print {
    body {
        background: white;
        font-size: 12px;
        line-height: 1.4;
    }
    
    .cv-container {
        box-shadow: none;
        padding: 20px;
        margin: 0;
        max-width: none;
        min-height: auto;
    }
    
    .no-print {
        display: none !important;
    }
    
    .section {
        page-break-inside: avoid;
        margin-bottom: 25px;
    }
    
    .experience-item {
        page-break-inside: avoid;
        margin-bottom: 20px;
    }
    
    .header {
        page-break-after: avoid;
        margin-bottom: 30px;
        padding-bottom: 20px;
    }
    
    .name {
        font-size: 2rem;
    }
    
    .section-title {
        font-size: 1.2rem;
        margin-bottom: 15px;
    }
    
    .contact-info {
        gap: 20px;
    }
    
    .skills-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 8px;
    }
    
    .skill-item {
        padding: 8px 12px;
        font-size: 0.9rem;
    }
    
    /* Ensure proper page breaks */
    h1, h2, h3 {
        page-break-after: avoid;
    }
    
    ul, ol {
        page-break-inside: avoid;
    }
    
    /* Prevent orphans and widows */
    p, li {
        orphans: 3;
        widows: 3;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .cv-container {
        padding: 20px;
        margin: 10px;
    }
    
    .name {
        font-size: 2rem;
    }
    
    .contact-info {
        flex-direction: column;
        gap: 15px;
    }
    
    .job-meta {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .skills-grid {
        grid-template-columns: 1fr;
    }
}
