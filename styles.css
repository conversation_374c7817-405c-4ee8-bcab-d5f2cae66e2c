/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #2c3e50;
    background-color: #f8fafc;
    font-size: 14px;
}

/* Container */
.cv-container {
    max-width: 210mm; /* A4 width */
    margin: 0 auto;
    background: white;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    padding: 30px;
    min-height: 297mm; /* A4 height */
}

/* Header */
.header {
    text-align: center;
    padding-bottom: 20px;
    margin-bottom: 30px;
    border-bottom: 2px solid #000000;
}

.header-content {
    text-align: center;
}

.name {
    font-size: 2.5rem;
    font-weight: 700;
    color: #000000;
    margin-bottom: 15px;
    letter-spacing: -0.5px;
}

.contact-info {
    display: flex;
    justify-content: center;
    gap: 30px;
    flex-wrap: wrap;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 1rem;
    color: #5a6c7d;
}

.icon {
    font-size: 1.1rem;
}

/* Sections */
.section {
    margin-bottom: 25px;
    page-break-inside: avoid;
}

.section-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: #000000;
    padding-bottom: 8px;
    margin-bottom: 15px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border-bottom: 1px solid #000000;
}

.section-content {
    padding-left: 0;
}

/* Professional Summary */
.section-content p {
    text-align: justify;
    line-height: 1.7;
    color: #34495e;
    font-size: 1rem;
    margin-bottom: 15px;
}

/* Technical Expertise */
.tech-categories {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

.tech-category {
    padding: 15px;
    border: 1px solid #000000;
    border-radius: 4px;
}

.tech-category h4 {
    font-size: 1rem;
    font-weight: 600;
    color: #000000;
    margin-bottom: 8px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.tech-category p {
    font-size: 0.9rem;
    color: #34495e;
    line-height: 1.5;
    margin: 0;
}

/* Experience */
.experience-item {
    margin-bottom: 25px;
    page-break-inside: avoid;
    padding: 20px;
    border: 1px solid #000000;
    border-radius: 4px;
}

.experience-header {
    margin-bottom: 15px;
}

.job-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: #000000;
    margin-bottom: 5px;
}

.job-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;
}

.company {
    font-weight: 500;
    color: #34495e;
    font-size: 1rem;
}

.duration {
    color: #7f8c8d;
    font-style: italic;
    font-size: 0.95rem;
    border: 1px solid #000000;
    padding: 4px 8px;
    border-radius: 3px;
}

.achievements {
    list-style: none;
    padding-left: 0;
}

.achievements li {
    position: relative;
    padding-left: 20px;
    margin-bottom: 8px;
    line-height: 1.6;
    color: #34495e;
}

.achievements li::before {
    content: "•";
    color: #000000;
    font-weight: bold;
    position: absolute;
    left: 0;
    top: 0;
}

/* Skills */
.skills-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 12px;
}

.skill-item {
    background: white;
    color: #000000;
    padding: 12px 16px;
    border: 1px solid #000000;
    border-radius: 4px;
    font-weight: 500;
    text-align: center;
    font-size: 0.9rem;
}

/* Education and Languages */
.education-list,
.languages-list {
    list-style: none;
    padding-left: 0;
    padding: 15px;
    border: 1px solid #000000;
    border-radius: 4px;
}

.education-list li,
.languages-list li {
    padding: 8px 0;
    color: #34495e;
    border-bottom: 1px solid #ecf0f1;
}

.education-list li:last-child,
.languages-list li:last-child {
    border-bottom: none;
}

/* Download Section */
.download-section {
    text-align: center;
    margin: 40px 0;
    padding: 20px;
}

.download-btn {
    background: white;
    color: #000000;
    border: 2px solid #000000;
    padding: 15px 30px;
    font-size: 1rem;
    font-weight: 600;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.download-btn:hover {
    background: #000000;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
}

/* Print Styles */
@media print {
    body {
        background: white;
        font-size: 12px;
        line-height: 1.4;
    }

    .cv-container {
        box-shadow: none;
        padding: 20px;
        margin: 0;
        max-width: none;
        min-height: auto;
    }

    .no-print {
        display: none !important;
    }

    .header {
        padding-bottom: 15px;
        margin-bottom: 25px;
        page-break-after: avoid;
        border-bottom: 2px solid #000000;
    }

    .name {
        font-size: 2rem;
        margin-bottom: 12px;
        color: #000000;
    }

    .contact-info {
        gap: 20px;
    }

    .contact-item {
        font-size: 0.9rem;
    }

    .section {
        page-break-inside: avoid;
        margin-bottom: 20px;
    }

    .section-title {
        font-size: 1.1rem;
        margin-bottom: 12px;
        border-bottom: 1px solid #000000;
    }

    .experience-item {
        page-break-inside: avoid;
        margin-bottom: 18px;
        padding: 15px;
        border: 1px solid #000000;
    }

    .job-title {
        font-size: 1rem;
    }

    .duration {
        border: 1px solid #000000;
        padding: 2px 6px;
        font-size: 0.8rem;
    }

    .achievements li {
        font-size: 0.9rem;
        margin-bottom: 5px;
    }

    .skills-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 8px;
    }

    .skill-item {
        padding: 8px 12px;
        font-size: 0.8rem;
        border: 1px solid #000000;
    }

    .tech-categories {
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
    }

    .tech-category {
        padding: 12px;
        border: 1px solid #000000;
        page-break-inside: avoid;
    }

    .tech-category h4 {
        font-size: 0.9rem;
        margin-bottom: 6px;
    }

    .tech-category p {
        font-size: 0.8rem;
        line-height: 1.4;
    }

    .education-list,
    .languages-list {
        padding: 12px;
        border: 1px solid #000000;
    }

    .education-list li,
    .languages-list li {
        font-size: 0.9rem;
        padding: 6px 0;
    }

    /* Ensure proper page breaks */
    h1, h2, h3 {
        page-break-after: avoid;
    }

    ul, ol {
        page-break-inside: avoid;
    }

    /* Prevent orphans and widows */
    p, li {
        orphans: 3;
        widows: 3;
    }

    .section-content p {
        margin-bottom: 10px;
        line-height: 1.5;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .cv-container {
        padding: 20px;
        margin: 10px;
    }
    
    .name {
        font-size: 2rem;
    }
    
    .contact-info {
        flex-direction: column;
        gap: 15px;
    }
    
    .job-meta {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .skills-grid {
        grid-template-columns: 1fr;
    }
}
