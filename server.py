#!/usr/bin/env python3
"""
Simple HTTP server to serve the CV locally
"""
import http.server
import socketserver
import webbrowser
import os
from pathlib import Path

PORT = 8000

class CVHandler(http.server.SimpleHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=os.getcwd(), **kwargs)

def main():
    # Check if files exist
    if not Path("index.html").exists():
        print("Error: index.html not found!")
        return
    
    if not Path("styles.css").exists():
        print("Error: styles.css not found!")
        return
    
    # Start server
    with socketserver.TCPServer(("", PORT), CVHandler) as httpd:
        print(f"🚀 CV Server running at http://localhost:{PORT}")
        print(f"📄 Open http://localhost:{PORT} to view your CV")
        print("💾 Click 'Download as PDF' button to save as PDF")
        print("🛑 Press Ctrl+C to stop the server")
        
        try:
            # Auto-open browser
            webbrowser.open(f"http://localhost:{PORT}")
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\n👋 Server stopped!")

if __name__ == "__main__":
    main()
