<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON>ra - Curriculum Vitae</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="cv-container">
        <!-- Header Section -->
        <header class="header">
            <div class="header-content">
                <h1 class="name"><PERSON></h1>
                <div class="contact-info">
                    <div class="contact-item">
                        <span class="icon">📞</span>
                        <span>+971 54 320 3973</span>
                    </div>
                    <div class="contact-item">
                        <span class="icon">✉️</span>
                        <span>ibrahimsb<PERSON><EMAIL></span>
                    </div>
                </div>
            </div>
        </header>

        <!-- Professional Summary -->
        <section class="section">
            <h2 class="section-title">Professional Summary</h2>
            <div class="section-content">
                <p>Results-driven IT Consultant and Technology Leader with extensive experience in AI integration, software architecture, and scalable web application development. Demonstrates a strong ability to lead cross-functional teams and implement digital solutions that align with strategic business goals.</p>
                <p>Proven track record in eCommerce, AI-powered platforms, and cloud infrastructure optimization. Adept at bridging technical execution with operational excellence to drive innovation and growth across multiple technology stacks and business domains.</p>
            </div>
        </section>

        <!-- Technical Expertise -->
        <section class="section">
            <h2 class="section-title">Technical Expertise</h2>
            <div class="section-content">
                <div class="tech-categories">
                    <div class="tech-category">
                        <h4>Frontend Technologies</h4>
                        <p>React.js, Next.js, Vue.js, TypeScript, JavaScript ES6+, HTML5, CSS3, Tailwind CSS</p>
                    </div>
                    <div class="tech-category">
                        <h4>Backend & Infrastructure</h4>
                        <p>Node.js, Express.js, Python, RESTful APIs, GraphQL, Microservices Architecture</p>
                    </div>
                    <div class="tech-category">
                        <h4>Cloud & DevOps</h4>
                        <p>AWS, Azure, Docker, Kubernetes, CI/CD Pipelines, Jenkins, Git, Linux Administration</p>
                    </div>
                    <div class="tech-category">
                        <h4>Databases & Analytics</h4>
                        <p>MongoDB, PostgreSQL, MySQL, Redis, Elasticsearch, Data Warehousing, ETL Processes</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Professional Experience -->
        <section class="section">
            <h2 class="section-title">Professional Experience</h2>
            <div class="section-content">
                <div class="experience-item">
                    <div class="experience-header">
                        <h3 class="job-title">Consultant</h3>
                        <div class="job-meta">
                            <span class="company">BloomingBox.com, Dubai, UAE</span>
                            <span class="duration">January 2023 – Present</span>
                        </div>
                    </div>
                    <ul class="achievements">
                        <li>Leading a cross-functional team of five developers, overseeing frontend and backend development with a focus on performance, maintainability, and delivery speed.</li>
                        <li>Managing the complete lifecycle of a high-traffic eCommerce platform (Next.js, Node.js), ensuring stability, security, and feature scalability.</li>
                        <li>Driving AI integration initiatives for workflow automation, user experience personalization, and data-driven decision-making.</li>
                        <li>Architecting platform upgrades to accommodate business growth and transaction volume increases.</li>
                        <li>Collaborating with product, marketing, and operations teams to translate business objectives into technological solutions.</li>
                        <li>Implementing CI/CD pipelines and DevOps practices to streamline secure, low-downtime deployments.</li>
                    </ul>
                </div>

                <div class="experience-item">
                    <div class="experience-header">
                        <h3 class="job-title">Co-founder & Technical Director</h3>
                        <div class="job-meta">
                            <span class="company">DMN Marketing Agency, Dubai, UAE</span>
                            <span class="duration">2014 – 2018</span>
                        </div>
                    </div>
                    <ul class="achievements">
                        <li>Established and scaled a digital agency delivering over 100 tailored websites and platforms for regional and global clients.</li>
                        <li>Directed a development team of 10, promoting innovation, agile methodologies, and technical excellence.</li>
                        <li>Led full-cycle project execution—from business development and requirement analysis to system design, delivery, and support.</li>
                        <li>Designed robust system architectures for industries including retail, logistics, and real estate.</li>
                        <li>Oversaw agency operations including hiring, budgeting, client relations, and technical strategy.</li>
                        <li>Championed the adoption of cutting-edge web frameworks and cloud services, elevating the agency's market positioning.</li>
                    </ul>
                </div>
            </div>
        </section>

        <!-- Key Skills -->
        <section class="section">
            <h2 class="section-title">Key Skills</h2>
            <div class="section-content">
                <div class="skills-grid">
                    <div class="skill-item">AI & Automation Integration</div>
                    <div class="skill-item">Web & eCommerce Development</div>
                    <div class="skill-item">Software Architecture & DevOps</div>
                    <div class="skill-item">Team & Project Leadership</div>
                    <div class="skill-item">Digital Transformation Strategy</div>
                    <div class="skill-item">Agile Product Management</div>
                </div>
            </div>
        </section>

        <!-- Education & Certifications -->
        <section class="section">
            <h2 class="section-title">Education & Certifications</h2>
            <div class="section-content">
                <ul class="education-list">
                    <li><strong>MSc, Computer Science</strong> – University of Atlanta</li>
                    <li><strong>BBA, Business Administration</strong> – University of Atlanta</li>
                    <li><strong>Certified Ethical Hacker</strong></li>
                    <li><strong>Microsoft Certified Systems Engineer (MCSE)</strong></li>
                </ul>
            </div>
        </section>

        <!-- Languages -->
        <section class="section">
            <h2 class="section-title">Languages</h2>
            <div class="section-content">
                <ul class="languages-list">
                    <li><strong>Arabic</strong> – Fluent</li>
                    <li><strong>English</strong> – Fluent</li>
                </ul>
            </div>
        </section>
    </div>

    <!-- Download Button -->
    <div class="download-section no-print">
        <button onclick="window.print()" class="download-btn">Download as PDF</button>
    </div>

    <script>
        // Ensure proper page breaks for PDF generation
        window.addEventListener('beforeprint', function() {
            document.body.classList.add('printing');
        });
        
        window.addEventListener('afterprint', function() {
            document.body.classList.remove('printing');
        });
    </script>
</body>
</html>
