# <PERSON> Bchara - Professional CV

A modern, professional CV built with HTML/CSS that can be downloaded as a PDF with proper page breaks and formatting.

## Features

- ✨ **Professional Design**: Clean, modern layout with excellent typography
- 📄 **PDF Ready**: Optimized for PDF generation with proper page breaks
- 📱 **Responsive**: Works perfectly on desktop, tablet, and mobile
- 🎨 **Print Optimized**: Special print styles ensure clean PDF output
- 🚀 **Easy to Use**: Simple one-click PDF download

## Quick Start

### Option 1: Using Python Server (Recommended)

1. Make sure you have Python installed
2. Run the server:
   ```bash
   python3 server.py
   ```
3. Your browser will automatically open to view the CV
4. Click "Download as PDF" to save as PDF

### Option 2: Direct File Opening

1. Simply double-click `index.html` to open in your browser
2. Use your browser's print function (Ctrl+P) and select "Save as PDF"

## PDF Download Instructions

1. Click the "Download as PDF" button on the webpage, OR
2. Use your browser's print function:
   - Press `Ctrl+P` (Windows/Linux) or `Cmd+P` (Mac)
   - Select "Save as PDF" as the destination
   - Choose "More settings" and ensure:
     - Paper size: A4
     - Margins: Default
     - Scale: 100%
     - Background graphics: Enabled

## File Structure

```
cv/
├── index.html          # Main CV HTML file
├── styles.css          # Professional styling with print optimization
├── server.py           # Local development server
└── README.md           # This file
```

## Customization

To modify the CV content:

1. Edit `index.html` to update personal information, experience, skills, etc.
2. Modify `styles.css` to change colors, fonts, or layout
3. The design is fully responsive and print-optimized

## Technical Features

- **Print-Specific CSS**: Ensures clean page breaks and proper formatting
- **Page Break Control**: Prevents text from being cut off mid-sentence
- **A4 Optimization**: Designed specifically for A4 paper size
- **Modern Typography**: Uses Inter font for excellent readability
- **Semantic HTML**: Properly structured for accessibility and SEO

## Browser Compatibility

- ✅ Chrome (recommended for PDF generation)
- ✅ Firefox
- ✅ Safari
- ✅ Edge

## Tips for Best PDF Output

1. Use Chrome or Edge for the best PDF generation results
2. Ensure "Background graphics" is enabled in print settings
3. Use 100% scale for optimal formatting
4. Select A4 paper size for international standard

---

**Created with ❤️ for Ibrahim Bchara**
